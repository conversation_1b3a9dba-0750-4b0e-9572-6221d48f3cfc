/**
 * Usage:
 *   1. Install dependencies: npm install puppeteer ytdl-core fluent-ffmpeg ffmpeg-static
 *   2. Run: node scrape_live_streams.js
 *
 * This script scrapes 100 YouTube live streams, downloads a 5-second MP4 snippet from each,
 * and saves them in the 'snippets' folder.
 */
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const ytdl = require('ytdl-core');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('ffmpeg-static');

// Folder to save video snippets
const OUTPUT_DIR = path.join(__dirname, 'snippets');
if (!fs.existsSync(OUTPUT_DIR)) {
    fs.mkdirSync(OUTPUT_DIR);
}

async function scrapeLiveStreams() {
    console.log('Launching browser...');
    const browser = await puppeteer.launch({ headless: true });
    const page = await browser.newPage();
    
    // Set a longer timeout for navigation
    await page.setDefaultNavigationTimeout(60000); // 60 seconds

    console.log('Navigating to YouTube Live...');
    try {
        await page.goto('https://www.youtube.com/channel/UC4R8DWoMoI7CAwX8_LjQHig', { waitUntil: 'networkidle2' });
    } catch (e) {
        console.warn('Navigation with networkidle2 failed, trying domcontentloaded...');
        await page.goto('https://www.youtube.com/channel/UC4R8DWoMoI7CAwX8_LjQHig', { waitUntil: 'domcontentloaded'});
    }

    // Handle cookie consent dialog
    try {
        console.log('Checking for cookie consent banner...');
        const cookieButtonSelector = 'button[aria-label*="Accept all"]';
        await page.waitForSelector(cookieButtonSelector, { timeout: 10000 });
        await page.click(cookieButtonSelector);
        console.log('Accepted cookie consent. Waiting for page to reload...');
        await page.waitForNavigation({ waitUntil: 'networkidle2' });
    } catch (error) {
        console.log('Cookie consent banner not found or could not be clicked.');
    }

    console.log('Waiting for video content to load...');
    const videoSelector = '#contents a#video-title';
    await page.waitForSelector(videoSelector, { timeout: 60000 }); // Wait up to 60 seconds

    console.log('Scraping video titles and URLs...');
    const streams = await page.evaluate(() => {
        const anchors = Array.from(document.querySelectorAll('#contents a#video-title'));
        return anchors.slice(0, 100).map(a => ({
            title: a.getAttribute('title').trim().replace(/[/\\?%*:|"<>]/g, ''), // Use title attribute for full title
            url: 'https://www.youtube.com' + a.getAttribute('href')
        }));
    });

    console.log('Closing browser...');
    await browser.close();
    return streams;
}

function downloadSnippet(url, title, outputPath) {
    return new Promise((resolve, reject) => {
        const stream = ytdl(url, { quality: 'highest', filter: 'audioandvideo', liveBuffer: 20000 });
        ffmpeg(stream)
            .setFfmpegPath(ffmpegPath)
            .format('mp4')
            .duration(5)
            .on('error', reject)
            .on('end', resolve)
            .save(outputPath);
    });
}

(async () => {
    try {
        const streams = await scrapeLiveStreams();
        if (streams.length === 0) {
            console.log('No live streams found. The page structure might have changed.');
            return;
        }
        console.log(`Found ${streams.length} live streams. Starting downloads...`);
        fs.writeFileSync(path.join(OUTPUT_DIR, 'streams.json'), JSON.stringify(streams, null, 2));

        for (let i = 0; i < streams.length; i++) {
            const { title, url } = streams[i];
            const filename = `${i + 1}_${title.substring(0, 40)}.mp4`;
            const outputPath = path.join(OUTPUT_DIR, filename);
            console.log(`[${i + 1}/${streams.length}] Downloading snippet: ${title}`);
            try {
                await downloadSnippet(url, title, outputPath);
                console.log(`  -> Saved: ${filename}`);
            } catch (err) {
                console.error(`  -> Failed to download ${title}:`, err.message.split('\n')[0]);
            }
        }
    } catch (error) {
        console.error('An unexpected error occurred:', error);
    }
    console.log('All done!');
})(); 