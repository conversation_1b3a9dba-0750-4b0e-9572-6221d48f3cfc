/**
 * Enhanced YouTube Live Stream Scraper
 *
 * Usage:
 *   1. Install dependencies: npm install puppeteer ytdl-core fluent-ffmpeg ffmpeg-static
 *   2. Run: node scrape_live_streams.js
 *
 * This script scrapes YouTube live streams from multiple sources, downloads 5-second MP4 snippets,
 * and saves them in the 'snippets' folder with robust error handling and retry mechanisms.
 */
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const ytdl = require('@distube/ytdl-core');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegPath = require('ffmpeg-static');

// Configuration
const CONFIG = {
    OUTPUT_DIR: path.join(__dirname, 'snippets'),
    MAX_STREAMS: 100,
    SNIPPET_DURATION: 5,
    TIMEOUT: 90000, // 90 seconds
    RETRY_ATTEMPTS: 3,
    HEADLESS: true,
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
};

// Create output directory
if (!fs.existsSync(CONFIG.OUTPUT_DIR)) {
    fs.mkdirSync(CONFIG.OUTPUT_DIR, { recursive: true });
}

// Multiple search strategies for finding live streams
const SEARCH_STRATEGIES = [
    {
        name: 'Gaming Live Streams',
        url: 'https://www.youtube.com/results?search_query=live+gaming&sp=EgJAAQ%253D%253D',
        description: 'Search for live gaming content'
    },
    {
        name: 'Music Live Streams',
        url: 'https://www.youtube.com/results?search_query=live+music&sp=EgJAAQ%253D%253D',
        description: 'Search for live music content'
    },
    {
        name: 'News Live Streams',
        url: 'https://www.youtube.com/results?search_query=live+news&sp=EgJAAQ%253D%253D',
        description: 'Search for live news content'
    },
    {
        name: 'General Live Streams',
        url: 'https://www.youtube.com/results?search_query=live&sp=EgJAAQ%253D%253D',
        description: 'Search for general live content'
    }
];

async function createBrowserPage() {
    console.log('🚀 Launching browser...');
    const browser = await puppeteer.launch({
        headless: CONFIG.HEADLESS,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-accelerated-2d-canvas',
            '--no-first-run',
            '--no-zygote',
            '--disable-gpu',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
    });

    const page = await browser.newPage();

    // Set user agent and viewport
    await page.setUserAgent(CONFIG.USER_AGENT);
    await page.setViewport({ width: 1920, height: 1080 });

    // Set timeouts
    await page.setDefaultNavigationTimeout(CONFIG.TIMEOUT);
    await page.setDefaultTimeout(CONFIG.TIMEOUT);

    return { browser, page };
}

async function handleCookieConsent(page) {
    try {
        console.log('🍪 Checking for cookie consent...');

        // Multiple possible cookie consent selectors
        const cookieSelectors = [
            'button[aria-label*="Accept all"]',
            'button[aria-label*="Accept"]',
            '[aria-label*="Accept all cookies"]',
            '[aria-label*="I agree"]',
            'button:has-text("Accept all")',
            'button:has-text("I agree")',
            '.VfPpkd-LgbsSe[aria-label*="Accept"]'
        ];

        for (const selector of cookieSelectors) {
            try {
                await page.waitForSelector(selector, { timeout: 5000 });
                await page.click(selector);
                console.log('✅ Cookie consent accepted');
                await page.waitForTimeout(2000);
                return true;
            } catch (e) {
                // Try next selector
                continue;
            }
        }

        console.log('ℹ️ No cookie consent found or needed');
        return false;
    } catch (error) {
        console.log('⚠️ Cookie consent handling failed:', error.message);
        return false;
    }
}

async function scrapeLiveStreamsFromStrategy(page, strategy) {
    console.log(`\n🔍 Trying strategy: ${strategy.name}`);
    console.log(`📍 URL: ${strategy.url}`);

    try {
        // Navigate to the search URL
        await page.goto(strategy.url, {
            waitUntil: 'domcontentloaded',
            timeout: CONFIG.TIMEOUT
        });

        // Handle cookie consent if needed
        await handleCookieConsent(page);

        // Wait for content to load
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Scroll to load more content
        console.log('📜 Scrolling to load more content...');
        for (let i = 0; i < 3; i++) {
            await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        console.log('🔎 Extracting live stream data...');

        // Extract live streams with multiple selector strategies
        const streams = await page.evaluate((strategyName) => {
            const results = [];

            // Multiple selector strategies for different YouTube layouts
            const selectors = [
                'a#video-title',
                'a[id="video-title"]',
                'h3 a[href*="/watch"]',
                'a[href*="/watch"]',
                '.ytd-video-renderer a',
                '.ytd-rich-item-renderer a',
                '[data-context-item-id] a'
            ];

            console.log('Starting video extraction...');

            for (const selector of selectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    console.log(`Found ${elements.length} elements with selector: ${selector}`);

                    for (let i = 0; i < elements.length && results.length < 50; i++) {
                        const element = elements[i];
                        try {
                            const href = element.getAttribute('href');

                            if (!href || !href.includes('/watch')) continue;

                            // Get title from multiple sources
                            const title = element.getAttribute('title') ||
                                        element.getAttribute('aria-label') ||
                                        element.textContent?.trim() ||
                                        element.querySelector('[title]')?.getAttribute('title') ||
                                        'Unknown Title';

                            if (title && title.length > 3) {
                                // Clean the URL by removing extra parameters
                                let cleanUrl = href.startsWith('http') ? href : `https://www.youtube.com${href}`;

                                // Extract just the video ID and create clean URL
                                const videoIdMatch = cleanUrl.match(/[?&]v=([^&]+)/);
                                if (videoIdMatch) {
                                    cleanUrl = `https://www.youtube.com/watch?v=${videoIdMatch[1]}`;
                                }

                                const cleanTitle = title.replace(/[/\\?%*:|"<>]/g, '').trim();

                                // Check if already added
                                if (!results.find(r => r.url === cleanUrl)) {
                                    results.push({
                                        title: cleanTitle,
                                        url: cleanUrl,
                                        strategy: strategyName
                                    });
                                }
                            }
                        } catch (e) {
                            console.log('Error processing element:', e.message);
                        }
                    }

                    if (results.length >= 20) break; // Found enough results
                } catch (e) {
                    console.log(`Error with selector ${selector}:`, e.message);
                }
            }

            console.log(`Extracted ${results.length} videos`);
            return results;
        }, strategy.name);

        console.log(`✅ Found ${streams.length} potential streams from ${strategy.name}`);
        return streams;

    } catch (error) {
        console.error(`❌ Strategy ${strategy.name} failed:`, error.message);
        return [];
    }
}

async function scrapeLiveStreams() {
    const { browser, page } = await createBrowserPage();
    let allStreams = [];

    try {
        // Try each strategy until we have enough streams
        for (const strategy of SEARCH_STRATEGIES) {
            if (allStreams.length >= CONFIG.MAX_STREAMS) break;

            const streams = await scrapeLiveStreamsFromStrategy(page, strategy);
            allStreams = allStreams.concat(streams);

            // Remove duplicates based on URL
            allStreams = allStreams.filter((stream, index, self) =>
                index === self.findIndex(s => s.url === stream.url)
            );

            console.log(`📊 Total unique streams collected: ${allStreams.length}`);

            if (allStreams.length >= CONFIG.MAX_STREAMS) {
                allStreams = allStreams.slice(0, CONFIG.MAX_STREAMS);
                break;
            }

            // Wait between strategies to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

    } catch (error) {
        console.error('❌ Error during scraping:', error);
    } finally {
        console.log('🔒 Closing browser...');
        await browser.close();
    }

    return allStreams;
}

async function validateVideoUrl(url) {
    try {
        console.log(`🔍 Validating: ${url}`);

        // Add options to help with YouTube's anti-bot measures
        const options = {
            requestOptions: {
                headers: {
                    'User-Agent': CONFIG.USER_AGENT,
                    'Accept-Language': 'en-US,en;q=0.9',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
            }
        };

        const info = await ytdl.getInfo(url, options);
        const isLive = info.videoDetails.isLiveContent || info.videoDetails.isLive;
        const hasVideo = info.formats.some(format => format.hasVideo && format.hasAudio);

        return {
            valid: hasVideo,
            isLive: isLive,
            title: info.videoDetails.title,
            duration: info.videoDetails.lengthSeconds
        };
    } catch (error) {
        console.log(`❌ Invalid URL: ${error.message.split('\n')[0]}`);
        return { valid: false, error: error.message };
    }
}

async function downloadSnippet(url, title, outputPath, retryCount = 0) {
    return new Promise(async (resolve, reject) => {
        try {
            console.log(`📥 Attempting download: ${title}`);

            // Validate the URL first
            const validation = await validateVideoUrl(url);
            if (!validation.valid) {
                throw new Error(`Invalid video: ${validation.error}`);
            }

            // Get video stream with better options
            const stream = ytdl(url, {
                quality: 'highest',
                filter: format => format.hasVideo && format.hasAudio,
                requestOptions: {
                    headers: {
                        'User-Agent': CONFIG.USER_AGENT,
                        'Accept-Language': 'en-US,en;q=0.9',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                    }
                }
            });

            // Set up FFmpeg with better error handling
            const ffmpegCommand = ffmpeg(stream)
                .setFfmpegPath(ffmpegPath)
                .format('mp4')
                .duration(CONFIG.SNIPPET_DURATION)
                .videoCodec('libx264')
                .audioCodec('aac')
                .size('1280x720')
                .on('start', (commandLine) => {
                    console.log(`🎬 FFmpeg started: ${commandLine}`);
                })
                .on('progress', (progress) => {
                    if (progress.percent) {
                        console.log(`📊 Progress: ${Math.round(progress.percent)}%`);
                    }
                })
                .on('error', (err) => {
                    console.error(`❌ FFmpeg error: ${err.message}`);
                    if (retryCount < CONFIG.RETRY_ATTEMPTS) {
                        console.log(`🔄 Retrying... (${retryCount + 1}/${CONFIG.RETRY_ATTEMPTS})`);
                        setTimeout(() => {
                            downloadSnippet(url, title, outputPath, retryCount + 1)
                                .then(resolve)
                                .catch(reject);
                        }, 2000);
                    } else {
                        reject(err);
                    }
                })
                .on('end', () => {
                    console.log(`✅ Download completed: ${title}`);
                    resolve();
                });

            // Save the file
            ffmpegCommand.save(outputPath);

        } catch (error) {
            if (retryCount < CONFIG.RETRY_ATTEMPTS) {
                console.log(`🔄 Retrying download... (${retryCount + 1}/${CONFIG.RETRY_ATTEMPTS})`);
                setTimeout(() => {
                    downloadSnippet(url, title, outputPath, retryCount + 1)
                        .then(resolve)
                        .catch(reject);
                }, 2000);
            } else {
                reject(error);
            }
        }
    });
}

async function processStreams(streams) {
    if (streams.length === 0) {
        console.log('❌ No streams found. YouTube structure may have changed or no live content available.');
        return;
    }

    console.log(`\n🎯 Found ${streams.length} potential streams. Starting processing...`);

    // Save stream metadata
    const metadataPath = path.join(CONFIG.OUTPUT_DIR, 'streams_metadata.json');
    fs.writeFileSync(metadataPath, JSON.stringify(streams, null, 2));
    console.log(`💾 Saved metadata to: ${metadataPath}`);

    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < streams.length; i++) {
        const { title, url, strategy } = streams[i];
        const safeTitle = title.substring(0, 50).replace(/[/\\?%*:|"<>]/g, '_');
        const filename = `${String(i + 1).padStart(3, '0')}_${safeTitle}.mp4`;
        const outputPath = path.join(CONFIG.OUTPUT_DIR, filename);

        console.log(`\n[${i + 1}/${streams.length}] 🎥 Processing: ${title}`);
        console.log(`📍 Source: ${strategy}`);
        console.log(`🔗 URL: ${url}`);

        try {
            // Skip if file already exists
            if (fs.existsSync(outputPath)) {
                console.log(`⏭️ File already exists, skipping: ${filename}`);
                successCount++;
                continue;
            }

            await downloadSnippet(url, title, outputPath);
            console.log(`✅ Saved: ${filename}`);
            successCount++;

            // Add delay between downloads to be respectful
            if (i < streams.length - 1) {
                console.log('⏳ Waiting before next download...');
                await new Promise(resolve => setTimeout(resolve, 3000));
            }

        } catch (err) {
            console.error(`❌ Failed to download "${title}":`, err.message.split('\n')[0]);
            failCount++;

            // Log failed download to file
            const failedPath = path.join(CONFIG.OUTPUT_DIR, 'failed_downloads.txt');
            const failedEntry = `${new Date().toISOString()} - ${title} - ${url} - ${err.message}\n`;
            fs.appendFileSync(failedPath, failedEntry);
        }
    }

    console.log(`\n📊 Download Summary:`);
    console.log(`✅ Successful: ${successCount}`);
    console.log(`❌ Failed: ${failCount}`);
    console.log(`📁 Output directory: ${CONFIG.OUTPUT_DIR}`);
}

// Main execution
(async () => {
    console.log('🚀 Enhanced YouTube Live Stream Scraper Starting...');
    console.log(`📁 Output directory: ${CONFIG.OUTPUT_DIR}`);
    console.log(`🎯 Target: ${CONFIG.MAX_STREAMS} streams`);
    console.log(`⏱️ Snippet duration: ${CONFIG.SNIPPET_DURATION} seconds\n`);

    try {
        const streams = await scrapeLiveStreams();
        await processStreams(streams);
    } catch (error) {
        console.error('💥 Unexpected error occurred:', error);
        console.error('Stack trace:', error.stack);
    }

    console.log('\n🎉 All done! Check the snippets folder for your downloaded videos.');
})();