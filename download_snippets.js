/**
 * YouTube Video Snippet Downloader
 * 
 * This script reads the found_streams.json file and downloads 5-second snippets
 * from each video using multiple download methods for maximum compatibility.
 */
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Configuration
const CONFIG = {
    INPUT_FILE: path.join(__dirname, 'snippets', 'found_streams.json'),
    OUTPUT_DIR: path.join(__dirname, 'snippets', 'videos'),
    SNIPPET_DURATION: 5,
    MAX_CONCURRENT: 3,
    RETRY_ATTEMPTS: 2,
    TIMEOUT: 60000 // 60 seconds per download
};

// Create output directory
if (!fs.existsSync(CONFIG.OUTPUT_DIR)) {
    fs.mkdirSync(CONFIG.OUTPUT_DIR, { recursive: true });
}

// Check if yt-dlp is available (better than youtube-dl)
async function checkDownloader() {
    try {
        await execAsync('yt-dlp --version');
        console.log('Using yt-dlp for downloads');
        return 'yt-dlp';
    } catch (error) {
        try {
            await execAsync('youtube-dl --version');
            console.log('Using youtube-dl for downloads');
            return 'youtube-dl';
        } catch (error2) {
            console.log('Neither yt-dlp nor youtube-dl found. Installing yt-dlp...');
            try {
                await execAsync('pip install yt-dlp');
                console.log('yt-dlp installed successfully');
                return 'yt-dlp';
            } catch (installError) {
                throw new Error('Could not install yt-dlp. Please install it manually: pip install yt-dlp');
            }
        }
    }
}

// Download a single video snippet
async function downloadSnippet(stream, downloader, index, total) {
    const safeTitle = stream.title
        .replace(/[/\\?%*:|"<>]/g, '_')
        .replace(/\s+/g, '_')
        .substring(0, 50);
    
    const filename = `${String(index + 1).padStart(3, '0')}_${safeTitle}.mp4`;
    const outputPath = path.join(CONFIG.OUTPUT_DIR, filename);
    
    // Skip if file already exists
    if (fs.existsSync(outputPath)) {
        console.log(`[${index + 1}/${total}] Skipping (already exists): ${filename}`);
        return { success: true, skipped: true };
    }
    
    console.log(`[${index + 1}/${total}] Downloading: ${stream.title}`);
    console.log(`URL: ${stream.url}`);
    
    try {
        let command;
        
        if (downloader === 'yt-dlp') {
            command = `yt-dlp "${stream.url}" ` +
                     `--format "best[height<=720]/best" ` +
                     `--output "${outputPath}" ` +
                     `--no-playlist ` +
                     `--no-warnings ` +
                     `--postprocessor-args "-t ${CONFIG.SNIPPET_DURATION}"`;
        } else {
            command = `youtube-dl "${stream.url}" ` +
                     `--format "best[height<=720]/best" ` +
                     `--output "${outputPath}" ` +
                     `--no-playlist`;
        }
        
        const { stdout, stderr } = await execAsync(command, {
            timeout: CONFIG.TIMEOUT
        });
        
        // Check if file was created and has reasonable size
        if (fs.existsSync(outputPath)) {
            const stats = fs.statSync(outputPath);
            if (stats.size > 1000) { // At least 1KB
                console.log(`Success: ${filename} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
                return { success: true, size: stats.size };
            } else {
                fs.unlinkSync(outputPath); // Remove tiny/empty file
                throw new Error('Downloaded file is too small');
            }
        } else {
            throw new Error('Output file was not created');
        }
        
    } catch (error) {
        console.error(`Failed: ${stream.title} - ${error.message}`);
        
        // Clean up any partial file
        if (fs.existsSync(outputPath)) {
            try {
                fs.unlinkSync(outputPath);
            } catch (e) {
                // Ignore cleanup errors
            }
        }
        
        return { success: false, error: error.message };
    }
}

// Alternative download method using ffmpeg directly
async function downloadWithFFmpeg(stream, index, total) {
    const safeTitle = stream.title
        .replace(/[/\\?%*:|"<>]/g, '_')
        .replace(/\s+/g, '_')
        .substring(0, 50);
    
    const filename = `${String(index + 1).padStart(3, '0')}_${safeTitle}_ffmpeg.mp4`;
    const outputPath = path.join(CONFIG.OUTPUT_DIR, filename);
    
    if (fs.existsSync(outputPath)) {
        console.log(`[${index + 1}/${total}] Skipping (already exists): ${filename}`);
        return { success: true, skipped: true };
    }
    
    console.log(`[${index + 1}/${total}] Trying FFmpeg direct download: ${stream.title}`);
    
    try {
        // Use ffmpeg to directly capture from the stream
        const command = `ffmpeg -i "${stream.url}" ` +
                       `-t ${CONFIG.SNIPPET_DURATION} ` +
                       `-c:v libx264 -c:a aac ` +
                       `-preset fast -crf 23 ` +
                       `-movflags +faststart ` +
                       `-y "${outputPath}"`;
        
        const { stdout, stderr } = await execAsync(command, {
            timeout: CONFIG.TIMEOUT
        });
        
        if (fs.existsSync(outputPath)) {
            const stats = fs.statSync(outputPath);
            if (stats.size > 1000) {
                console.log(`FFmpeg Success: ${filename} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
                return { success: true, size: stats.size };
            }
        }
        
        throw new Error('FFmpeg output file not created or too small');
        
    } catch (error) {
        console.error(`FFmpeg failed: ${stream.title} - ${error.message}`);
        
        if (fs.existsSync(outputPath)) {
            try {
                fs.unlinkSync(outputPath);
            } catch (e) {
                // Ignore cleanup errors
            }
        }
        
        return { success: false, error: error.message };
    }
}

// Process downloads with concurrency control
async function processDownloads(streams, downloader) {
    const results = {
        total: streams.length,
        successful: 0,
        failed: 0,
        skipped: 0,
        errors: []
    };
    
    console.log(`\nStarting download of ${streams.length} video snippets...`);
    console.log(`Output directory: ${CONFIG.OUTPUT_DIR}`);
    console.log(`Snippet duration: ${CONFIG.SNIPPET_DURATION} seconds\n`);
    
    // Process in batches to avoid overwhelming the system
    for (let i = 0; i < streams.length; i += CONFIG.MAX_CONCURRENT) {
        const batch = streams.slice(i, i + CONFIG.MAX_CONCURRENT);
        const promises = batch.map(async (stream, batchIndex) => {
            const globalIndex = i + batchIndex;
            
            // Try primary downloader first
            let result = await downloadSnippet(stream, downloader, globalIndex, streams.length);
            
            // If primary failed, try FFmpeg direct method
            if (!result.success && !result.skipped) {
                console.log(`Retrying with FFmpeg: ${stream.title}`);
                result = await downloadWithFFmpeg(stream, globalIndex, streams.length);
            }
            
            return { stream, result, index: globalIndex };
        });
        
        const batchResults = await Promise.all(promises);
        
        // Process results
        batchResults.forEach(({ stream, result, index }) => {
            if (result.skipped) {
                results.skipped++;
            } else if (result.success) {
                results.successful++;
            } else {
                results.failed++;
                results.errors.push({
                    title: stream.title,
                    url: stream.url,
                    error: result.error
                });
            }
        });
        
        // Wait between batches
        if (i + CONFIG.MAX_CONCURRENT < streams.length) {
            console.log(`Completed batch ${Math.floor(i / CONFIG.MAX_CONCURRENT) + 1}, waiting before next batch...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
    }
    
    return results;
}

// Main execution
(async () => {
    console.log('YouTube Video Snippet Downloader Starting...');
    
    try {
        // Check if input file exists
        if (!fs.existsSync(CONFIG.INPUT_FILE)) {
            console.error(`Input file not found: ${CONFIG.INPUT_FILE}`);
            console.log('Please run simple_scraper.js first to generate the streams file.');
            process.exit(1);
        }
        
        // Load streams data
        const streamsData = JSON.parse(fs.readFileSync(CONFIG.INPUT_FILE, 'utf8'));
        console.log(`Loaded ${streamsData.length} streams from ${CONFIG.INPUT_FILE}`);
        
        // Check downloader availability
        const downloader = await checkDownloader();
        
        // Process downloads
        const results = await processDownloads(streamsData, downloader);
        
        // Display results
        console.log('\n' + '='.repeat(50));
        console.log('DOWNLOAD SUMMARY');
        console.log('='.repeat(50));
        console.log(`Total streams: ${results.total}`);
        console.log(`Successful downloads: ${results.successful}`);
        console.log(`Skipped (already existed): ${results.skipped}`);
        console.log(`Failed downloads: ${results.failed}`);
        console.log(`Output directory: ${CONFIG.OUTPUT_DIR}`);
        
        // Save error log if there were failures
        if (results.errors.length > 0) {
            const errorLogPath = path.join(CONFIG.OUTPUT_DIR, 'download_errors.json');
            fs.writeFileSync(errorLogPath, JSON.stringify(results.errors, null, 2));
            console.log(`Error details saved to: ${errorLogPath}`);
        }
        
        console.log('\nDownload process completed!');
        
    } catch (error) {
        console.error('Unexpected error occurred:', error);
        process.exit(1);
    }
})();
