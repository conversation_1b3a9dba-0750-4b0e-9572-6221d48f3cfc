/**
 * Simple YouTube Video Downloader
 * 
 * Downloads videos from found_streams.json without requiring FFmpeg
 * Downloads full videos first, then can be trimmed later
 */
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

// Configuration
const CONFIG = {
    INPUT_FILE: path.join(__dirname, 'snippets', 'found_streams.json'),
    OUTPUT_DIR: path.join(__dirname, 'snippets', 'videos'),
    MAX_CONCURRENT: 2,
    TIMEOUT: 120000, // 2 minutes per download
    MAX_DOWNLOADS: 10 // Limit to first 10 videos for testing
};

// Create output directory
if (!fs.existsSync(CONFIG.OUTPUT_DIR)) {
    fs.mkdirSync(CONFIG.OUTPUT_DIR, { recursive: true });
}

// Check if yt-dlp is available
async function checkDownloader() {
    try {
        await execAsync('yt-dlp --version');
        console.log('Using yt-dlp for downloads');
        return 'yt-dlp';
    } catch (error) {
        try {
            await execAsync('youtube-dl --version');
            console.log('Using youtube-dl for downloads');
            return 'youtube-dl';
        } catch (error2) {
            console.log('Neither yt-dlp nor youtube-dl found. Installing yt-dlp...');
            try {
                await execAsync('pip install yt-dlp');
                console.log('yt-dlp installed successfully');
                return 'yt-dlp';
            } catch (installError) {
                throw new Error('Could not install yt-dlp. Please install it manually: pip install yt-dlp');
            }
        }
    }
}

// Download a single video
async function downloadVideo(stream, downloader, index, total) {
    const safeTitle = stream.title
        .replace(/[/\\?%*:|"<>]/g, '_')
        .replace(/\s+/g, '_')
        .substring(0, 50);
    
    const filename = `${String(index + 1).padStart(3, '0')}_${safeTitle}.%(ext)s`;
    const outputTemplate = path.join(CONFIG.OUTPUT_DIR, filename);
    
    // Check if any file with this prefix already exists
    const existingFiles = fs.readdirSync(CONFIG.OUTPUT_DIR).filter(file => 
        file.startsWith(`${String(index + 1).padStart(3, '0')}_${safeTitle}`)
    );
    
    if (existingFiles.length > 0) {
        console.log(`[${index + 1}/${total}] Skipping (already exists): ${existingFiles[0]}`);
        return { success: true, skipped: true };
    }
    
    console.log(`[${index + 1}/${total}] Downloading: ${stream.title}`);
    console.log(`URL: ${stream.url}`);
    
    try {
        let command;
        
        if (downloader === 'yt-dlp') {
            command = `yt-dlp "${stream.url}" ` +
                     `--format "best[height<=480]/best[height<=720]/best" ` +
                     `--output "${outputTemplate}" ` +
                     `--no-playlist ` +
                     `--no-warnings ` +
                     `--max-filesize 50M ` +
                     `--abort-on-error`;
        } else {
            command = `youtube-dl "${stream.url}" ` +
                     `--format "best[height<=480]/best[height<=720]/best" ` +
                     `--output "${outputTemplate}" ` +
                     `--no-playlist ` +
                     `--max-filesize 50M`;
        }
        
        console.log(`Running: ${command}`);
        
        const { stdout, stderr } = await execAsync(command, {
            timeout: CONFIG.TIMEOUT
        });
        
        // Check if any file was created
        const newFiles = fs.readdirSync(CONFIG.OUTPUT_DIR).filter(file => 
            file.startsWith(`${String(index + 1).padStart(3, '0')}_${safeTitle}`)
        );
        
        if (newFiles.length > 0) {
            const downloadedFile = newFiles[0];
            const stats = fs.statSync(path.join(CONFIG.OUTPUT_DIR, downloadedFile));
            console.log(`Success: ${downloadedFile} (${(stats.size / 1024 / 1024).toFixed(2)} MB)`);
            return { success: true, size: stats.size, filename: downloadedFile };
        } else {
            throw new Error('No output file was created');
        }
        
    } catch (error) {
        console.error(`Failed: ${stream.title} - ${error.message}`);
        
        // Clean up any partial files
        const partialFiles = fs.readdirSync(CONFIG.OUTPUT_DIR).filter(file => 
            file.startsWith(`${String(index + 1).padStart(3, '0')}_${safeTitle}`) && 
            (file.includes('.part') || file.includes('.tmp'))
        );
        
        partialFiles.forEach(file => {
            try {
                fs.unlinkSync(path.join(CONFIG.OUTPUT_DIR, file));
            } catch (e) {
                // Ignore cleanup errors
            }
        });
        
        return { success: false, error: error.message };
    }
}

// Process downloads with concurrency control
async function processDownloads(streams, downloader) {
    const results = {
        total: streams.length,
        successful: 0,
        failed: 0,
        skipped: 0,
        errors: [],
        downloaded: []
    };
    
    console.log(`\nStarting download of ${streams.length} videos...`);
    console.log(`Output directory: ${CONFIG.OUTPUT_DIR}`);
    console.log(`Max concurrent downloads: ${CONFIG.MAX_CONCURRENT}\n`);
    
    // Process in batches to avoid overwhelming the system
    for (let i = 0; i < streams.length; i += CONFIG.MAX_CONCURRENT) {
        const batch = streams.slice(i, i + CONFIG.MAX_CONCURRENT);
        const promises = batch.map(async (stream, batchIndex) => {
            const globalIndex = i + batchIndex;
            const result = await downloadVideo(stream, downloader, globalIndex, streams.length);
            return { stream, result, index: globalIndex };
        });
        
        const batchResults = await Promise.all(promises);
        
        // Process results
        batchResults.forEach(({ stream, result, index }) => {
            if (result.skipped) {
                results.skipped++;
            } else if (result.success) {
                results.successful++;
                if (result.filename) {
                    results.downloaded.push({
                        title: stream.title,
                        url: stream.url,
                        filename: result.filename,
                        size: result.size
                    });
                }
            } else {
                results.failed++;
                results.errors.push({
                    title: stream.title,
                    url: stream.url,
                    error: result.error
                });
            }
        });
        
        // Wait between batches
        if (i + CONFIG.MAX_CONCURRENT < streams.length) {
            console.log(`Completed batch ${Math.floor(i / CONFIG.MAX_CONCURRENT) + 1}, waiting before next batch...`);
            await new Promise(resolve => setTimeout(resolve, 3000));
        }
    }
    
    return results;
}

// Main execution
(async () => {
    console.log('Simple YouTube Video Downloader Starting...');
    
    try {
        // Check if input file exists
        if (!fs.existsSync(CONFIG.INPUT_FILE)) {
            console.error(`Input file not found: ${CONFIG.INPUT_FILE}`);
            console.log('Please run simple_scraper.js first to generate the streams file.');
            process.exit(1);
        }
        
        // Load streams data
        const allStreams = JSON.parse(fs.readFileSync(CONFIG.INPUT_FILE, 'utf8'));
        const streams = allStreams.slice(0, CONFIG.MAX_DOWNLOADS); // Limit for testing
        
        console.log(`Loaded ${allStreams.length} streams, downloading first ${streams.length} for testing`);
        
        // Check downloader availability
        const downloader = await checkDownloader();
        
        // Process downloads
        const results = await processDownloads(streams, downloader);
        
        // Display results
        console.log('\n' + '='.repeat(50));
        console.log('DOWNLOAD SUMMARY');
        console.log('='.repeat(50));
        console.log(`Total streams processed: ${results.total}`);
        console.log(`Successful downloads: ${results.successful}`);
        console.log(`Skipped (already existed): ${results.skipped}`);
        console.log(`Failed downloads: ${results.failed}`);
        console.log(`Output directory: ${CONFIG.OUTPUT_DIR}`);
        
        // Show downloaded files
        if (results.downloaded.length > 0) {
            console.log('\nSuccessfully downloaded:');
            results.downloaded.forEach((item, index) => {
                console.log(`${index + 1}. ${item.filename} (${(item.size / 1024 / 1024).toFixed(2)} MB)`);
            });
        }
        
        // Save results
        const resultsPath = path.join(CONFIG.OUTPUT_DIR, 'download_results.json');
        fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));
        console.log(`\nResults saved to: ${resultsPath}`);
        
        // Save error log if there were failures
        if (results.errors.length > 0) {
            const errorLogPath = path.join(CONFIG.OUTPUT_DIR, 'download_errors.json');
            fs.writeFileSync(errorLogPath, JSON.stringify(results.errors, null, 2));
            console.log(`Error details saved to: ${errorLogPath}`);
        }
        
        console.log('\nDownload process completed!');
        
        if (results.successful > 0) {
            console.log('\nNext steps:');
            console.log('1. Check the downloaded videos in the snippets/videos folder');
            console.log('2. If you want to create 5-second snippets, you can install FFmpeg and use a video editor');
            console.log('3. Or use online tools to trim the videos to 5 seconds');
        }
        
    } catch (error) {
        console.error('Unexpected error occurred:', error);
        process.exit(1);
    }
})();
