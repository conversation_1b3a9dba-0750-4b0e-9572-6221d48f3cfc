# YouTube Live Stream Scraper

A Node.js tool for scraping YouTube live streams and downloading video snippets.

## Features

- Scrapes live streams from multiple YouTube categories (Gaming, Music, News)
- Finds 50+ active live streams reliably
- Exports clean stream data to JSON
- Downloads video snippets (requires additional setup)

## Files

- `simple_scraper.js` - Main scraper (emoji-free version)
- `simple_downloader.js` - Basic video downloader
- `download_snippets.js` - Advanced snippet downloader (requires FFmpeg)
- `scrape_live_streams.js` - Original enhanced scraper

## Quick Start

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run the scraper:**
   ```bash
   node simple_scraper.js
   ```

3. **Check results:**
   - Stream data saved to: `snippets/found_streams.json`
   - Contains 50 live stream URLs with metadata

## Downloading Videos

### Option 1: Manual Download (Recommended)
1. Open `snippets/found_streams.json`
2. Copy any video URL
3. Use online tools like:
   - https://ytdl-org.github.io/youtube-dl/
   - https://yt-dlp.org/
   - Online YouTube downloaders

### Option 2: Automated Download (Advanced)
**Requirements:**
- Python with pip
- FFmpeg installed

**Steps:**
1. Install yt-dlp: `pip install yt-dlp`
2. Install FFmpeg: https://ffmpeg.org/download.html
3. Run: `node simple_downloader.js`

### Creating 5-Second Snippets

**Method 1: Online Tools**
- Upload downloaded videos to online video editors
- Trim to first 5 seconds
- Export as MP4

**Method 2: FFmpeg Command Line**
```bash
ffmpeg -i input_video.mp4 -t 5 -c copy output_snippet.mp4
```

**Method 3: Video Editing Software**
- Use any video editor (DaVinci Resolve, OpenShot, etc.)
- Import video and trim to 5 seconds

## Troubleshooting

### Common Issues

1. **"FFmpeg not found" error:**
   - Install FFmpeg from https://ffmpeg.org/
   - Add to system PATH

2. **"m3u8 download detected" error:**
   - Live streams use special format requiring FFmpeg
   - Install FFmpeg or use manual download method

3. **Download failures:**
   - YouTube has anti-bot measures
   - Some streams may be geo-restricted
   - Try different streams from the JSON file

### Success Tips

- The scraper works reliably and finds 50+ streams
- Focus on using the scraped URLs manually first
- For bulk downloading, ensure FFmpeg is properly installed
- Live streams are harder to download than regular videos

## Output Structure

`snippets/found_streams.json` contains:
```json
[
  {
    "title": "Stream Title",
    "url": "https://www.youtube.com/watch?v=VIDEO_ID",
    "strategy": "Gaming Live Streams",
    "found_at": "2025-01-20T10:30:00.000Z"
  }
]
```

## Technical Notes

- Uses Puppeteer for web scraping
- Implements multiple search strategies
- Handles YouTube's dynamic content loading
- Removes emojis for clean console output
- Robust error handling and retry logic

## Next Steps

1. Run `simple_scraper.js` to get live stream URLs
2. Choose your preferred download method
3. Create 5-second snippets using your preferred tool
4. Enjoy your YouTube live stream collection!
