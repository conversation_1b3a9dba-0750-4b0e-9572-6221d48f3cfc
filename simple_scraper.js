/**
 * Simple YouTube Live Stream Finder
 * 
 * This script finds YouTube live streams and saves the information to a JSON file.
 * Use this to test if the scraping part works before attempting downloads.
 */
const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
    OUTPUT_DIR: path.join(__dirname, 'snippets'),
    MAX_STREAMS: 50,
    TIMEOUT: 60000,
    USER_AGENT: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
};

// Create output directory
if (!fs.existsSync(CONFIG.OUTPUT_DIR)) {
    fs.mkdirSync(CONFIG.OUTPUT_DIR, { recursive: true });
}

// Search strategies for finding live streams
const SEARCH_STRATEGIES = [
    {
        name: 'Gaming Live Streams',
        url: 'https://www.youtube.com/results?search_query=live+gaming&sp=EgJAAQ%253D%253D'
    },
    {
        name: 'Music Live Streams', 
        url: 'https://www.youtube.com/results?search_query=live+music&sp=EgJAAQ%253D%253D'
    },
    {
        name: 'News Live Streams',
        url: 'https://www.youtube.com/results?search_query=live+news&sp=EgJAAQ%253D%253D'
    }
];

async function createBrowserPage() {
    console.log('🚀 Launching browser...');
    const browser = await puppeteer.launch({ 
        headless: true,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu'
        ]
    });
    
    const page = await browser.newPage();
    await page.setUserAgent(CONFIG.USER_AGENT);
    await page.setViewport({ width: 1920, height: 1080 });
    await page.setDefaultNavigationTimeout(CONFIG.TIMEOUT);
    
    return { browser, page };
}

async function scrapeLiveStreamsFromStrategy(page, strategy) {
    console.log(`\n🔍 Trying strategy: ${strategy.name}`);
    console.log(`📍 URL: ${strategy.url}`);
    
    try {
        await page.goto(strategy.url, { 
            waitUntil: 'domcontentloaded',
            timeout: CONFIG.TIMEOUT 
        });
        
        // Wait for content to load
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Scroll to load more content
        console.log('📜 Scrolling to load more content...');
        for (let i = 0; i < 3; i++) {
            await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
        console.log('🔎 Extracting live stream data...');
        
        const streams = await page.evaluate((strategyName) => {
            const results = [];
            
            // Multiple selector strategies
            const selectors = [
                'a#video-title',
                'a[id="video-title"]', 
                'h3 a[href*="/watch"]',
                'a[href*="/watch"]'
            ];
            
            for (const selector of selectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    console.log(`Found ${elements.length} elements with selector: ${selector}`);
                    
                    for (let i = 0; i < Math.min(elements.length, 20); i++) {
                        const element = elements[i];
                        try {
                            const href = element.getAttribute('href');
                            
                            if (!href || !href.includes('/watch')) continue;
                            
                            const title = element.getAttribute('title') || 
                                        element.getAttribute('aria-label') || 
                                        element.textContent?.trim() ||
                                        'Unknown Title';
                            
                            if (title && title.length > 3) {
                                // Clean the URL
                                let cleanUrl = href.startsWith('http') ? href : `https://www.youtube.com${href}`;
                                const videoIdMatch = cleanUrl.match(/[?&]v=([^&]+)/);
                                if (videoIdMatch) {
                                    cleanUrl = `https://www.youtube.com/watch?v=${videoIdMatch[1]}`;
                                }
                                
                                const cleanTitle = title.replace(/[/\\?%*:|"<>]/g, '').trim();
                                
                                if (!results.find(r => r.url === cleanUrl)) {
                                    results.push({
                                        title: cleanTitle,
                                        url: cleanUrl,
                                        strategy: strategyName,
                                        found_at: new Date().toISOString()
                                    });
                                }
                            }
                        } catch (e) {
                            console.log('Error processing element:', e.message);
                        }
                    }
                    
                    if (results.length >= 15) break;
                } catch (e) {
                    console.log(`Error with selector ${selector}:`, e.message);
                }
            }
            
            return results;
        }, strategy.name);
        
        console.log(`✅ Found ${streams.length} potential streams from ${strategy.name}`);
        return streams;
        
    } catch (error) {
        console.error(`❌ Strategy ${strategy.name} failed:`, error.message);
        return [];
    }
}

async function findLiveStreams() {
    const { browser, page } = await createBrowserPage();
    let allStreams = [];
    
    try {
        for (const strategy of SEARCH_STRATEGIES) {
            if (allStreams.length >= CONFIG.MAX_STREAMS) break;
            
            const streams = await scrapeLiveStreamsFromStrategy(page, strategy);
            allStreams = allStreams.concat(streams);
            
            // Remove duplicates
            allStreams = allStreams.filter((stream, index, self) => 
                index === self.findIndex(s => s.url === stream.url)
            );
            
            console.log(`📊 Total unique streams collected: ${allStreams.length}`);
            
            if (allStreams.length >= CONFIG.MAX_STREAMS) {
                allStreams = allStreams.slice(0, CONFIG.MAX_STREAMS);
                break;
            }
            
            await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
    } catch (error) {
        console.error('❌ Error during scraping:', error);
    } finally {
        console.log('🔒 Closing browser...');
        await browser.close();
    }
    
    return allStreams;
}

// Main execution
(async () => {
    console.log('🚀 Simple YouTube Live Stream Finder Starting...');
    console.log(`📁 Output directory: ${CONFIG.OUTPUT_DIR}`);
    console.log(`🎯 Target: ${CONFIG.MAX_STREAMS} streams\n`);
    
    try {
        const streams = await findLiveStreams();
        
        if (streams.length === 0) {
            console.log('❌ No streams found.');
            return;
        }
        
        console.log(`\n✅ Found ${streams.length} live streams!`);
        
        // Save results
        const outputFile = path.join(CONFIG.OUTPUT_DIR, 'found_streams.json');
        fs.writeFileSync(outputFile, JSON.stringify(streams, null, 2));
        console.log(`💾 Saved stream data to: ${outputFile}`);
        
        // Display first few streams
        console.log('\n📺 Sample streams found:');
        streams.slice(0, 5).forEach((stream, index) => {
            console.log(`${index + 1}. ${stream.title}`);
            console.log(`   URL: ${stream.url}`);
            console.log(`   Source: ${stream.strategy}\n`);
        });
        
        console.log(`\n🎉 Successfully found ${streams.length} YouTube live streams!`);
        console.log('📁 Check the found_streams.json file for the complete list.');
        
    } catch (error) {
        console.error('💥 Unexpected error occurred:', error);
    }
})();
